
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import { ref } from 'vue';
import { GoogleGenAI, GenerateContentResponse, Session, Modality, StartSensitivity, EndSensitivity, LiveServerMessage, SpeechConfig } from '@google/genai';
import { ImageGeneratorService } from '../image-generator-service';
// DialogService, DialogServiceCallbacks, DialogConfig removed
// decodeAudioData removed
import { VOICE_OPTIONS } from '../ai-data';
import { Genre } from '../ai-data-types';
import { buildCharacterGenerationPrompt, buildSceneImagePromptLLMPrompt, buildSceneNarrationLLMPrompt, buildNarrationSpeechPrompt } from '../prompt-builder';
import type { useAdventureState } from './useAdventureState'; // For type inference
import { DialogConfig, DialogServiceCallbacks } from '../dialog-service';
// CharacterImage import removed as companionImageRef is removed

type AdventureState = ReturnType<typeof useAdventureState>;

export function useAdventureSetup(
    ai: GoogleGenAI,
    imageGeneratorService: ImageGeneratorService,
    state: AdventureState
    // companionImageRef: Ref<InstanceType<typeof CharacterImage> | null> // Removed
) {
    const {
        selectedGenre,
        generatedCharacterType, generatedRole, generatedMood, generatedStyle,
        AIGeneratedVoiceName, generatedCharacterName, generatedCharacterDescription,
        generatedDetailedVisualDescription, generatedCoreTrait, generatedMainWant,
        generatedKeyFlaw, generatedVoicePromptInstruction,
        currentContextualMood, currentContextualStyle,
        initialSceneNarratorVoice, initialSceneImagePrompt, initialSceneImageUrl,
        initialSceneNarration, rawSceneNarrationLLMPrompt,
        isLoadingAdventure, isLoadingCharacter, isLoadingScene, isNarrating,
        isGameScreenActive, selectedDialogModel, selectedImageModel,
        actualCharacterGenerationLLMPrompt,
        isCharacterGenerated, resetFullAdventureState: _resetFullAdventureState,
        isConnectingAudio 
    } = state;

    const isSmallScreen = ref(window.innerWidth < 1024); 
    const resetFullAdventureState = () => _resetFullAdventureState(isSmallScreen.value);


    const handleQuotaExceeded = (source: 'characterImage' | 'sceneImage' | 'dialog') => {
        console.warn(`[AdventureSetup] API quota exceeded from ${source}.`);
        if (source === 'dialog') {
             alert("Dialog API quota exceeded. The adventure cannot continue. Check Google Cloud project quotas.");
             resetFullAdventureState();
        } else if (source === 'characterImage') {
            // The CompanionInfoPanel will show its own error message for character image quota.
            // This function is now more for logging or broader state changes if needed.
            console.warn("Character image generation quota exceeded. The component should display an error.");
        } else if (source === 'sceneImage') {
             console.warn("Scene image generation quota exceeded.");
        }
    };
    
    const playInitialNarration = (): Promise<void> => {
        return new Promise(async (resolve, reject) => {
            console.log("[PlayNarration] Starting. Narration text:", initialSceneNarration.value ? initialSceneNarration.value.substring(0,50)+'...' : 'N/A', "Voice:", initialSceneNarratorVoice.value);
            if (!initialSceneNarration.value || !initialSceneNarratorVoice.value) {
                console.warn("[PlayNarration] Narration data missing. Resolving immediately.");
                resolve();
                return;
            }

            // isNarrating.value is set to true by the caller (generateInitialScene) before this function.
            console.log("[PlayNarration] isNarrating should be true (set by caller). Current value:", isNarrating.value);
            
            const { DialogService } = await import('../dialog-service');
            const { decodeAudioData: narrationDecodeAudioData } = await import('../audio-utils.ts'); // Changed to relative path


            let narrationDialogService: InstanceType<typeof DialogService> | null = null;
            let narrationSession: Session | null = null;
            let narrationAudioContext: AudioContext | null = null;
            let narrationOutputNode: GainNode | null = null;
            let narrationActiveSources: AudioBufferSourceNode[] = [];
            let narrationNextStartTime = 0;
            let narrationPlaybackAttempted = false;

            const cleanupNarrationResources = async (reason: string) => {
                console.log(`[PlayNarration] Cleaning up narration resources. Reason: ${reason}`);
                narrationActiveSources.forEach(source => {
                    try { source.stop(); } catch (e) { console.warn("[PlayNarration] Error stopping audio source during cleanup:", e); }
                });
                narrationActiveSources = [];
                if (narrationSession && narrationDialogService) {
                    try { 
                        console.log("[PlayNarration] Closing narration session.");
                        await narrationDialogService.closeSession(narrationSession); 
                    } catch(e) { console.warn("[PlayNarration] Error closing narration session during cleanup:", e); }
                }
                if (narrationAudioContext && narrationAudioContext.state !== 'closed') {
                   try { 
                       console.log("[PlayNarration] Closing narration AudioContext.");
                       await narrationAudioContext.close(); 
                    } catch(e) { console.warn("[PlayNarration] Error closing narration AudioContext during cleanup:", e); }
                }
                narrationDialogService = null;
                narrationSession = null;
                narrationAudioContext = null;
                narrationOutputNode = null;
                console.log("[PlayNarration] Narration resources cleaned up.");
            };

            try {
                narrationDialogService = new DialogService(process.env.API_KEY!);
                narrationAudioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 24000 });
                narrationOutputNode = narrationAudioContext.createGain();
                narrationOutputNode.connect(narrationAudioContext.destination);
                console.log("[PlayNarration] AudioContext and GainNode for narration created.");

                const narrationSessionConfig: DialogConfig = { 
                    model: selectedDialogModel.value,
                    responseModalities: [Modality.AUDIO],
                    realtimeInputConfig: {
                        automaticActivityDetection: {
                            disabled: true,
                            startOfSpeechSensitivity: StartSensitivity.START_SENSITIVITY_LOW,
                            endOfSpeechSensitivity: EndSensitivity.END_SENSITIVITY_LOW,
                        },
                    },
                    speechConfig: {} 
                };

                const narrationSpeechPrompt = buildNarrationSpeechPrompt(initialSceneNarration.value, initialSceneNarratorVoice.value);
                console.log("[PlayNarration] Narration speech prompt prepared:", narrationSpeechPrompt.substring(0,150) + "...");

                const narrationSessionCallbacks: DialogServiceCallbacks = { 
                    onopen: () => {
                        console.log("[PlayNarration] Narration session opened.");
                        if (narrationSession && narrationDialogService) {
                            console.log("[PlayNarration] Sending narration content to DialogService.");
                            narrationDialogService.sendClientContent(narrationSession, {
                                turns: narrationSpeechPrompt,
                                turnComplete: true
                            });
                        } else {
                            console.error("[PlayNarration] onopen: Session or service is null.");
                            // Ensure isNarrating is checked before cleanup to avoid race conditions if promise already settled
                            if (isNarrating.value) {
                                cleanupNarrationResources("session_null_onopen").then(() => {
                                    if(isNarrating.value) reject(new Error("Narration session or service became null unexpectedly in onopen."));
                                });
                            }
                        }
                    },
                    onmessage: async (message: LiveServerMessage) => {
                        if (!isNarrating.value) return; // Stop processing if narration was cancelled/finished
                        console.log("[PlayNarration] Narration onmessage received:", JSON.stringify(message).substring(0, 200) + "...");
                        const audio = message.serverContent?.modelTurn?.parts[0]?.inlineData;
                        const turnComplete = message.serverContent?.turnComplete;
                        
                        if (audio && narrationAudioContext && narrationOutputNode) {
                            narrationPlaybackAttempted = true;
                            console.log("[PlayNarration] Received narration audio chunk.");
                            if (narrationAudioContext.state === 'suspended') {
                                try {
                                    console.log("[PlayNarration] Resuming narration AudioContext.");
                                    await narrationAudioContext.resume();
                                } catch (resumeError) {
                                     console.error("[PlayNarration] Error resuming narration AudioContext:", resumeError);
                                }
                            }
                            narrationNextStartTime = Math.max(narrationNextStartTime, narrationAudioContext.currentTime);
                            const audioBuffer = await narrationDecodeAudioData(audio.data, narrationAudioContext, 24000, 1);
                            const source = narrationAudioContext.createBufferSource();
                            source.buffer = audioBuffer;
                            narrationActiveSources.push(source);
                            source.onended = () => {
                                const index = narrationActiveSources.indexOf(source);
                                if (index > -1) narrationActiveSources.splice(index, 1);
                                console.log(`[PlayNarration] Audio source ended. Active sources: ${narrationActiveSources.length}`);
                            };
                            source.connect(narrationOutputNode);
                            source.start(narrationNextStartTime);
                            narrationNextStartTime += audioBuffer.duration;
                            console.log(`[PlayNarration] Narration audio chunk scheduled. Next start time: ${narrationNextStartTime}`);
                        }

                        if (turnComplete) {
                            if (!isNarrating.value) return; 
                            console.log("[PlayNarration] Narration turnComplete received.");
                            const checkAudioDoneInterval = 200;
                            const maxWaitTimeForAudioCompletion = narrationPlaybackAttempted ? 5000 : 3000; // Increased from 200ms to 3000ms
                            let timeWaited = 0;

                            const checkAudioDone = () => {
                                if (!isNarrating.value) { // Check again in case narration was stopped during timeout
                                    console.log("[PlayNarration] checkAudioDone: Narration stopped, aborting check.");
                                    return;
                                }
                                if (narrationActiveSources.length === 0 && narrationAudioContext && narrationAudioContext.currentTime >= narrationNextStartTime - 0.25) { 
                                    console.log("[PlayNarration] All narration audio sources finished playing.");
                                    if (isNarrating.value) {
                                        cleanupNarrationResources("audio_finished").then(() => {
                                            if (isNarrating.value) {
                                                console.log("[PlayNarration] Resolving promise (audio finished).");
                                                resolve();
                                            }
                                        });
                                    }
                                } else if (timeWaited >= maxWaitTimeForAudioCompletion) {
                                    console.warn(`[PlayNarration] Max wait time (${maxWaitTimeForAudioCompletion}ms) for audio completion reached. Playback attempted: ${narrationPlaybackAttempted}. Forcing cleanup and resolving.`);
                                     if (isNarrating.value) {
                                        cleanupNarrationResources("max_wait_reached").then(() => {
                                           if (isNarrating.value) {
                                                console.log(`[PlayNarration] Resolving promise (max wait reached). Playback attempted: ${narrationPlaybackAttempted}`);
                                                if (!narrationPlaybackAttempted) {
                                                   console.warn("[PlayNarration] No audio was ever played back during narration despite turnComplete.");
                                                }
                                                resolve(); 
                                            }
                                        });
                                    }
                                }
                                else {
                                    timeWaited += checkAudioDoneInterval;
                                    setTimeout(checkAudioDone, checkAudioDoneInterval);
                                }
                            };
                            setTimeout(checkAudioDone, narrationPlaybackAttempted ? 100 : 50); // Initial delay
                        }
                    },
                    onerror: async (e: ErrorEvent) => {
                        console.error("[PlayNarration] Narration session error:", e.message, e);
                        if (isNarrating.value) {
                            await cleanupNarrationResources("session_error");
                            if (isNarrating.value) {
                                console.log("[PlayNarration] Rejecting promise due to session error.");
                                reject(new Error(`Narration playback failed: ${e.message || 'Unknown session error'}`));
                            }
                        }
                    },
                    onclose: async (e: CloseEvent) => {
                        console.log("[PlayNarration] Narration session closed. Reason:", e.reason, "Code:", e.code);
                        
                        if (!isNarrating.value) {
                            console.log("[PlayNarration] onclose: Not in narrating state, cleanup likely done or promise settled.");
                            return;
                        }

                        if (e.code !== 1000) { // Abnormal closure
                           console.log(`[PlayNarration] Abnormal session close (code ${e.code}) while narrating.`);
                           await cleanupNarrationResources(`session_closed_abnormally (code ${e.code})`);
                           if (isNarrating.value) reject(new Error(`Narration session closed prematurely: ${e.reason || 'Unknown reason'}`));
                        } 
                        else { // Normal closure (e.code === 1000)
                            if (!narrationPlaybackAttempted) {
                                console.warn("[PlayNarration] Narration session closed normally (1000) but no audio messages processed. Assuming no audio and resolving.");
                            } else {
                                console.log("[PlayNarration] Narration session closed normally (1000). Narration assumed complete or handled by turnComplete's checkAudioDone.");
                            }
                            await cleanupNarrationResources(`session_closed_normally (code 1000)`);
                            if (isNarrating.value) resolve(); 
                        }
                    }
                };
                console.log("[PlayNarration] Connecting to DialogService for narration...");
                narrationSession = await narrationDialogService.connect(narrationSessionConfig, narrationSessionCallbacks);
                console.log("[PlayNarration] DialogService connected for narration.");

            } catch (err) {
                console.error("[PlayNarration] Error setting up or playing narration via DialogService:", err);
                if (isNarrating.value) {
                    await cleanupNarrationResources("setup_error");
                    if (isNarrating.value) {
                        console.log("[PlayNarration] Rejecting promise due to setup error.");
                        reject(err instanceof Error ? err : new Error(String(err)));
                    }
                }
            }
        });
    };

    const generateInitialScene = async (startInteractiveCallback: () => Promise<void>) => {
        if (!isCharacterGenerated.value || !selectedGenre.value) {
            console.error("[AdventureSetup] Cannot generate scene: character or genre missing.");
            if (!isLoadingCharacter.value) resetFullAdventureState();
            return;
        }
        console.log("[AdventureSetup] Generating initial scene...");
        isLoadingScene.value = true;
        initialSceneImageUrl.value = '';
        const appBackground = document.getElementById('app-background');
        if (appBackground) appBackground.style.backgroundImage = 'none';
        
        try {
            console.log("[AdventureSetup] Generating scene image prompt (LLM)...");
            const sceneImgPromptLLMPrompt = buildSceneImagePromptLLMPrompt(selectedGenre.value, generatedCharacterName.value, generatedDetailedVisualDescription.value);
            const responseSceneImgPrompt = await ai.models.generateContent({
                model: 'gemini-2.5-flash-preview-04-17',
                contents: sceneImgPromptLLMPrompt
            });
            initialSceneImagePrompt.value = responseSceneImgPrompt.text.trim().replace(/^Image Prompt:\s*/i, '');
            console.log("[AdventureSetup] Scene image prompt generated:", initialSceneImagePrompt.value);

            console.log("[AdventureSetup] Generating scene image (Imagen)...");
            try {
                const sceneImageResponse = await imageGeneratorService.generate(selectedImageModel.value, initialSceneImagePrompt.value, { numberOfImages: 1, outputMimeType: 'image/jpeg' });
                if (sceneImageResponse.generatedImages && sceneImageResponse.generatedImages[0]?.image?.imageBytes) {
                    initialSceneImageUrl.value = `data:image/jpeg;base64,${sceneImageResponse.generatedImages[0].image.imageBytes}`;
                    if (appBackground) appBackground.style.backgroundImage = `url(${initialSceneImageUrl.value})`;
                    console.log("[AdventureSetup] Scene image generated and set.");
                } else {
                     console.warn("[AdventureSetup] Scene image generation succeeded but no image bytes found.");
                     initialSceneImageUrl.value = '';
                }
            } catch (imgError) {
                console.warn("[AdventureSetup] Error generating scene image (Imagen):", imgError);
                if (imgError instanceof Error && (imgError.message.includes('RESOURCE_EXHAUSTED') || imgError.message.includes('429'))) {
                    handleQuotaExceeded('sceneImage');
                }
                initialSceneImageUrl.value = '';
            }

            console.log("[AdventureSetup] Generating scene narration (LLM)...");
            const availableVoiceNames = VOICE_OPTIONS.map(v => v.name);
            rawSceneNarrationLLMPrompt.value = buildSceneNarrationLLMPrompt(selectedGenre.value, generatedCharacterName.value, generatedCharacterDescription.value, initialSceneImagePrompt.value, availableVoiceNames);
            const responseSceneNarration = await ai.models.generateContent({
                model: 'gemini-2.5-flash-preview-04-17',
                contents: rawSceneNarrationLLMPrompt.value,
                config: { responseMimeType: "application/json" }
            });
            let jsonStrNarration = responseSceneNarration.text.trim();
            const fenceRegexNar = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
            const matchNar = jsonStrNarration.match(fenceRegexNar);
            if (matchNar && matchNar[2]) jsonStrNarration = matchNar[2].trim();
            const parsedNarrationData = JSON.parse(jsonStrNarration);
            console.log("[AdventureSetup] Parsed scene narration data:", parsedNarrationData);

            if (parsedNarrationData.narrationText && typeof parsedNarrationData.narrationText === 'string' &&
                parsedNarrationData.narratorVoiceName && availableVoiceNames.includes(parsedNarrationData.narratorVoiceName)) {
                initialSceneNarration.value = parsedNarrationData.narrationText;
                initialSceneNarratorVoice.value = parsedNarrationData.narratorVoiceName;
                console.log("[AdventureSetup] Scene narration text and voice set.");
            } else {
                console.error("[AdventureSetup] AI response for scene narration missing fields or invalid voice.");
                throw new Error("AI response for scene narration missing fields or invalid voice name.");
            }
            
            isLoadingScene.value = false;
            console.log("[AdventureSetup] Scene data ready. isLoadingScene:", isLoadingScene.value);

            isNarrating.value = true;
            console.log("[AdventureSetup] Set isNarrating to true before playing narration.");
            try {
                if (initialSceneNarration.value && initialSceneNarratorVoice.value) {
                    console.log("[AdventureSetup] Attempting to play initial narration...");
                    const narrationTimeout = 20000; // 20 seconds
                    const narrationPromise = playInitialNarration();
                    const timeoutPromise = new Promise<void>((_, rejectP) =>
                        setTimeout(() => {
                             console.warn("[AdventureSetup] Narration playback explicitly timed out after " + narrationTimeout/1000 + " seconds");
                             // Only reject if we are still in narrating state, otherwise promise might have been settled.
                             if (isNarrating.value) rejectP(new Error("Narration playback timed out"));
                        }, narrationTimeout)
                    );
                    await Promise.race([narrationPromise, timeoutPromise]);
                    console.log("[AdventureSetup] Initial narration completed or bypassed successfully.");
                } else {
                     console.log("[AdventureSetup] No narration text or voice, skipping playback.");
                }
            } catch (narrationError) {
                console.error("[AdventureSetup] Error during initial narration process:", narrationError);
                alert(`Error playing initial narration: ${narrationError instanceof Error ? narrationError.message : String(narrationError)}. Proceeding without it.`);
            } finally {
                isNarrating.value = false;
                console.log("[AdventureSetup] Narration phase finished, set isNarrating to false. Current value:", isNarrating.value);
            }
            
            console.log("[AdventureSetup] Proceeding to activate game screen.");
            isLoadingAdventure.value = false;
            isGameScreenActive.value = true;
            if (isSmallScreen.value) document.body.style.overflow = 'hidden';
            console.log("[AdventureSetup] Game screen active. isCharacterGenerated:", isCharacterGenerated.value);

            if (isGameScreenActive.value && isCharacterGenerated.value && !isConnectingAudio.value) {
                console.log("[AdventureSetup] Conditions met, calling startInteractiveCallback.");
                await startInteractiveCallback(); 
            } else {
                 console.log(`[AdventureSetup] Conditions not met for startInteractiveCallback. isGameScreenActive: ${isGameScreenActive.value}, isCharacterGenerated: ${isCharacterGenerated.value}, isConnectingAudio: ${isConnectingAudio.value}`);
            }

        } catch (e) {
            console.error("[AdventureSetup] Error during initial scene generation (LLM/overall):", e);
            alert(`Failed to generate scene: ${e instanceof Error ? e.message : 'Unknown error'}. Please try again.`);
            resetFullAdventureState();
        }
    };

    const handleStartAdventureSetup = async (startInteractiveCallback: () => Promise<void>) => {
        if (!selectedGenre.value) {
            alert("Please select a genre first.");
            return;
        }
        const genreToUse = selectedGenre.value; 
        console.log("[AdventureSetup] Starting adventure setup for genre:", genreToUse);
        
        resetFullAdventureState();
        selectedGenre.value = genreToUse; 

        isLoadingAdventure.value = true;
        isLoadingCharacter.value = true;
        isGameScreenActive.value = false;

        const availableVoiceNames = VOICE_OPTIONS.map(v => v.name);
        actualCharacterGenerationLLMPrompt.value = buildCharacterGenerationPrompt(selectedGenre.value, availableVoiceNames);
        state.actualVoicePrompt.value = ''; 
        state.actualCharacterImagePrompt.value = ''; 

        try {
            console.log("[AdventureSetup] Generating character (LLM)...");
            const responseCharGen: GenerateContentResponse = await ai.models.generateContent({
                model: 'gemini-2.5-flash-preview-04-17',
                contents: actualCharacterGenerationLLMPrompt.value,
                config: { responseMimeType: "application/json" }
            });
            console.log("[AdventureSetup] Character generation LLM response received.");

            let jsonStrChar = responseCharGen.text.trim();
            const fenceRegexChar = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
            const matchChar = jsonStrChar.match(fenceRegexChar);
            if (matchChar && matchChar[2]) jsonStrChar = matchChar[2].trim();
            const parsedCharData = JSON.parse(jsonStrChar);
            console.log("[AdventureSetup] Parsed character data:", parsedCharData);

            if (
                parsedCharData.characterType && typeof parsedCharData.characterType === 'string' &&
                parsedCharData.role && typeof parsedCharData.role === 'string' &&
                parsedCharData.mood && typeof parsedCharData.mood === 'string' &&
                parsedCharData.style && typeof parsedCharData.style === 'string' &&
                parsedCharData.voiceName && availableVoiceNames.includes(parsedCharData.voiceName) &&
                parsedCharData.characterName && typeof parsedCharData.characterName === 'string' &&
                parsedCharData.characterDescription && typeof parsedCharData.characterDescription === 'string' &&
                parsedCharData.detailedVisualDescription && typeof parsedCharData.detailedVisualDescription === 'string' &&
                parsedCharData.coreTrait && typeof parsedCharData.coreTrait === 'string' &&
                parsedCharData.mainWant && typeof parsedCharData.mainWant === 'string' &&
                parsedCharData.keyFlaw && typeof parsedCharData.keyFlaw === 'string' &&
                parsedCharData.voicePromptInstruction && typeof parsedCharData.voicePromptInstruction === 'string'
            ) {
                generatedCharacterType.value = parsedCharData.characterType;
                generatedRole.value = parsedCharData.role;
                generatedMood.value = parsedCharData.mood;
                generatedStyle.value = parsedCharData.style;
                AIGeneratedVoiceName.value = parsedCharData.voiceName;
                generatedCharacterName.value = parsedCharData.characterName;
                generatedCharacterDescription.value = parsedCharData.characterDescription;
                generatedDetailedVisualDescription.value = parsedCharData.detailedVisualDescription;
                generatedCoreTrait.value = parsedCharData.coreTrait;
                generatedMainWant.value = parsedCharData.mainWant;
                generatedKeyFlaw.value = parsedCharData.keyFlaw;
                generatedVoicePromptInstruction.value = parsedCharData.voicePromptInstruction;
                currentContextualMood.value = '';
                currentContextualStyle.value = '';
                isLoadingCharacter.value = false;
                console.log("[AdventureSetup] Character generation successful. Proceeding to scene generation.");
                await generateInitialScene(startInteractiveCallback);
            } else {
                console.error("[AdventureSetup] AI response for character missing fields or invalid:", parsedCharData);
                throw new Error("Failed to generate a complete character. AI response was incomplete/invalid.");
            }
        } catch (e) {
            console.error("[AdventureSetup] Error during character generation (LLM):", e);
            alert(`Failed to start adventure: ${e instanceof Error ? e.message : 'Unknown error during character generation'}. Please try again.`);
            resetFullAdventureState();
        }
    };

    const handleRegenerateSceneImage = async () => {
        if (!initialSceneImagePrompt.value || !selectedGenre.value) {
            alert("Cannot regenerate scene image: initial prompt or genre missing.");
            return;
        }
        console.log("[AdventureSetup] Regenerating scene image...");
        isLoadingScene.value = true;
        const appBackground = document.getElementById('app-background');
        try {
            const sceneImageResponse = await imageGeneratorService.generate(selectedImageModel.value, initialSceneImagePrompt.value, { numberOfImages: 1, outputMimeType: 'image/jpeg' });
            if (sceneImageResponse.generatedImages && sceneImageResponse.generatedImages[0]?.image?.imageBytes) {
                initialSceneImageUrl.value = `data:image/jpeg;base64,${sceneImageResponse.generatedImages[0].image.imageBytes}`;
                if (appBackground) appBackground.style.backgroundImage = `url(${initialSceneImageUrl.value})`;
                console.log("[AdventureSetup] Scene image regenerated successfully.");
            } else {
                 console.warn("[AdventureSetup] Scene image regeneration returned no image bytes.");
            }
        } catch (imgError) {
            console.warn("[AdventureSetup] Error regenerating scene image:", imgError);
             if (imgError instanceof Error && (imgError.message.includes('RESOURCE_EXHAUSTED') || imgError.message.includes('429'))) {
                handleQuotaExceeded('sceneImage');
                 alert("Scene image regeneration failed due to API quota. Please try again later.");
            } else {
                alert("Failed to regenerate scene image. Please see console for details.");
            }
        } finally {
            isLoadingScene.value = false;
        }
    };
    
    const handleGenreSelected = (genre: Genre) => {
      state.selectedGenre.value = genre;
      if (state.isGameScreenActive.value || state.isCharacterGenerated.value || state.isLoadingAdventure.value) {
        console.log("[AdventureSetup] Genre changed while adventure active/loading. Resetting state.");
        _resetFullAdventureState(isSmallScreen.value);
      } else {
        console.log("[AdventureSetup] Genre selected on initial screen. No reset needed yet.");
      }
    };


    return {
        handleStartAdventureSetup,
        handleRegenerateSceneImage,
        handleGenreSelected,
        handleQuotaExceeded
    };
}