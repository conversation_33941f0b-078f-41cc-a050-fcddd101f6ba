/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import { defineComponent, ref, onMounted, onUnmounted, watch } from 'vue';
import { ImageGeneratorService } from '../image-generator-service';
import { buildImagePrompt } from '../prompt-builder';
import { DEFAULT_IMAGE_MODEL } from '../ai-config';
// CHARACTER_ATTRIBUTES, VISUAL_ACCESSORIES removed
import { Genre, CharacterType } from '../ai-data-types'; // CharacterType might become illustrative

export default defineComponent({
  props: {
    // character: { type: String as () => CharacterType | '', required: true }, // Replaced by detailedVisualDescription
    // role: { type: String, default: '' }, // Replaced
    // mood: { type: String, default: '' }, // Replaced
    // style: { type: String, default: '' }, // Replaced
    detailedVisualDescription: { type: String, required: true }, // AI-generated comprehensive visual description
    model: { type: String, default: DEFAULT_IMAGE_MODEL },
    genre: { type: String as () => Genre | '', required: true },
  },
  emits: ['update:imagePrompt', 'quota-exceeded'],
  setup(props, { emit }) {
    const imageUrl = ref('');
    const status = ref('');
    const isLoading = ref(false);
    const errorMessage = ref('');
    let imageGeneratorService: ImageGeneratorService;

    const initializeImageGeneratorService = () => {
      try {
        if (!imageGeneratorService) {
          imageGeneratorService = new ImageGeneratorService(process.env.API_KEY!);
        }
      } catch (e) {
        console.error("Error initializing ImageGeneratorService:", e);
        errorMessage.value = `Failed to initialize ImageGeneratorService: ${e instanceof Error ? e.message : String(e)}`;
        throw e;
      }
    };

    const checkKeyPixels = (imageData: string): Promise<boolean> => {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            console.error("Failed to get 2D context for image check.");
            resolve(false);
            return;
          }
          ctx.drawImage(img, 0, 0);
          
          const keyPixels = [
            { x: 0, y: 0 }, { x: img.width - 1, y: 0 }, { x: Math.floor(img.width / 2), y: 0 },
            { x: 0, y: img.height - 1 }, { x: img.width - 1, y: img.height - 1 }, { x: Math.floor(img.width / 2), y: img.height - 1 }
          ];

          for (const pixel of keyPixels) {
            const pixelData = ctx.getImageData(pixel.x, pixel.y, 1, 1).data;
            const hasContent = pixelData[0] <= 250 || pixelData[1] <= 250 || pixelData[2] <= 250;
            if (hasContent) { 
              resolve(true); 
              return;
            }
          }
          resolve(false); 
        };
        img.onerror = (e) => {
            console.error("Error loading image for pixel check:", e);
            resolve(false);
        }
        img.src = imageData;
      });
    };

    const loadKeyMessage = (message: string) => {
      imageUrl.value = ''; 
      errorMessage.value = message;
      console.warn("CharacterImage displaying key message:", message);
    };
    
    const generateImage = async () => {
      if (!props.detailedVisualDescription || !props.genre) {
          isLoading.value = false;
          status.value = "Detailed visual description or genre not provided.";
          console.warn("generateImage called without detailedVisualDescription or genre prop.");
          return;
      }
      try {
        initializeImageGeneratorService();
      } catch (initError) {
        isLoading.value = false;
        status.value = "Error initializing image service.";
        return;
      }
      isLoading.value = true;
      status.value = 'Generating...';
      imageUrl.value = ''; 
      errorMessage.value = '';

      const prompt = buildImagePrompt(
        props.detailedVisualDescription,
        props.genre as Genre 
      );
      emit('update:imagePrompt', prompt);

      try {
        const response = await imageGeneratorService.generate(props.model, prompt, { numberOfImages: 3, outputMimeType: 'image/jpeg' });

        if (response.generatedImages && response.generatedImages.length > 0) {
          let foundValidImage = false;
          let lastSrc = '';
          for (const imgObj of response.generatedImages) {
            if (imgObj.image?.imageBytes) {
              const src = `data:image/jpeg;base64,${imgObj.image.imageBytes}`;
              lastSrc = src;
              // eslint-disable-next-line no-await-in-loop
              const hasContent = await checkKeyPixels(src); 
              if (hasContent) {
                imageUrl.value = src;
                status.value = 'Done!';
                foundValidImage = true;
                break; 
              } else {
                console.warn("Generated image was considered blank after pixel check.");
              }
            }
          }
          if (!foundValidImage && lastSrc) { 
            imageUrl.value = lastSrc;
            status.value = 'Generated image might be blank, using last attempt.';
            console.warn("No clearly valid image found, using the last generated image.");
          } else if (!foundValidImage) {
             console.error('No valid image data received or all images were blank.');
             throw new Error('No valid image data received or all images were blank.');
          }
        } else {
          console.error('No image data received from Imagen. Response:', response);
          throw new Error('No image data received from Imagen.');
        }
      } catch (e) {
        const message = e instanceof Error ? e.message : 'Unknown image generation error.';
        console.error("Image generation error:", message, e);
        if (message.includes('RESOURCE_EXHAUSTED') || message.includes('429')) {
          loadKeyMessage('Imagen API quota exceeded. Please check your project settings.');
          emit('quota-exceeded');
        } else if (message.includes("SAFETY")) {
          errorMessage.value = "Image generation failed due to safety policies. Try a different prompt.";
        }
        else {
          errorMessage.value = `Image generation failed: ${message}`;
        }
        imageUrl.value = ''; 
      } finally {
        isLoading.value = false;
      }
    };

    onMounted(async () => {
      try {
        initializeImageGeneratorService();
      } catch (e) {
        return;
      }
      if (props.detailedVisualDescription && props.genre) {
        await generateImage();
      }
    });

    watch(() => [props.detailedVisualDescription, props.model, props.genre], async () => {
        if (props.detailedVisualDescription && props.genre) {
            await generateImage();
        }
    }, {deep: true});


    onUnmounted(() => {
      if (imageUrl.value && imageUrl.value.startsWith('blob:')) {
        try {
            URL.revokeObjectURL(imageUrl.value);
        } catch (e) {
            console.warn("Error revoking object URL:", e);
        }
      }
    });
    
    const triggerGenerateImage = async () => {
        await generateImage();
    };


    return {
      imageUrl, status, isLoading, errorMessage, loadKeyMessage, 
      triggerGenerateImage 
    };
  },
  template: `
    <div class="relative w-full aspect-square flex items-center justify-center rounded-lg overflow-hidden bg-gray-700">
      <div v-if="errorMessage" class="absolute top-2 left-2 right-2 z-30 text-red-400 bg-gray-800/80 p-2 rounded text-xs text-center">{{ errorMessage }}</div>
      <div v-show="isLoading" class="absolute z-20 inset-0 flex items-center justify-center bg-gray-800/50">
        <div class="relative w-12 h-12">
          <div class="absolute inset-0 border-4 border-gray-500/50 rounded-full border-t-transparent animate-spin"></div>
        </div>
      </div>
      <img v-if="imageUrl && !isLoading" class="transform scale-100 w-full h-full object-contain transition-opacity duration-500 opacity-100" :src="imageUrl" alt="Generated Character"/>
      <div v-if="!imageUrl && !isLoading && !errorMessage" class="text-gray-400">Image will appear here</div>
    </div>
  `
});
