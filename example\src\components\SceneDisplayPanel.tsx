/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import { defineComponent, ref, watch } from 'vue';

export default defineComponent({
  props: {
    sceneImageUrl: { type: String, default: '' },
    sceneNarration: { type: String, default: '' },
    isNarrating: { type: Boolean, default: false }, // To potentially style narration text while being read
    isLoading: { type: Boolean, default: false }, // To show loading state for image/narration
  },
  setup(props) {
    const isNarrationExpanded = ref(false);
    const narrationKey = ref(0); // Used to force re-render of narration if content changes

    const toggleNarration = () => {
      isNarrationExpanded.value = !isNarrationExpanded.value;
    };

    watch(() => props.sceneNarration, () => {
        isNarrationExpanded.value = false; // Collapse on new narration
        narrationKey.value++; // Force re-render if needed for reactivity on dynamic class
    });

    return {
      isNarrationExpanded,
      toggleNarration,
      narrationKey,
    };
  },
  template: `
    <div class="w-full flex flex-col items-center justify-start p-4 bg-gray-800 rounded-lg shadow-xl relative">
      <!-- Scene Image -->
      <div class="w-full aspect-[16/10] rounded-lg overflow-hidden bg-gray-700 mb-4 shadow-inner relative">
        <div v-if="isLoading && !sceneImageUrl" class="absolute inset-0 flex flex-col items-center justify-center bg-gray-700/80 z-10">
          <div class="relative w-12 h-12 mb-2">
              <div class="absolute inset-0 border-4 border-gray-500 rounded-full border-t-transparent animate-spin"></div>
          </div>
          <div class="text-gray-400">Loading scene image...</div>
        </div>
        <img v-if="sceneImageUrl" :src="sceneImageUrl" alt="Current Adventure Scene" class="w-full h-full object-cover transition-opacity duration-500 ease-in-out" :class="{'opacity-0': isLoading && !sceneImageUrl, 'opacity-100': !isLoading && sceneImageUrl}"/>
        <div v-if="!sceneImageUrl && !isLoading" class="w-full h-full flex items-center justify-center text-gray-500">
          Scene image will appear here.
        </div>
      </div>

      <!-- Scene Narration -->
      <div v-if="isLoading && !sceneNarration" class="w-full p-3 bg-gray-700 rounded-md text-center text-gray-400 animate-pulse">
        Loading narration...
      </div>
      <div v-if="sceneNarration && !isLoading" :key="narrationKey"
           class="w-full p-3 bg-gray-700/80 backdrop-blur-sm rounded-md text-gray-200 text-sm lg:text-base leading-relaxed shadow transition-all duration-300 ease-in-out"
           :class="[isNarrationExpanded ? 'expanded-text' : 'collapsed-text', {'ring-2 ring-purple-500 ring-offset-2 ring-offset-gray-800': isNarrating}]"
           @click="toggleNarration"
           role="button"
           tabindex="0"
           aria-expanded="isNarrationExpanded"
           aria-live="polite">
        <p>{{ sceneNarration }}</p>
      </div>
       <div v-if="!sceneNarration && !isLoading" class="w-full p-3 bg-gray-700 rounded-md text-center text-gray-400">
        Narration will appear here.
      </div>
    </div>
  `
});
