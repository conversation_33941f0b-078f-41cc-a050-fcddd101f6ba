/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */
import { GoogleGenAI, GenerateImagesResponse, GenerateImagesConfig } from '@google/genai';

export interface ImageConfig extends GenerateImagesConfig {
    numberOfImages: number;
    outputMimeType: 'image/jpeg' | 'image/png';
}

export class ImageGeneratorService {
  private client: GoogleGenAI;

  constructor(apiKey: string) {
     if (!apiKey) {
      throw new Error("API key is required to initialize ImageGeneratorService.");
    }
    this.client = new GoogleGenAI({ apiKey });
  }

  async generate(model: string, prompt: string, config: ImageConfig): Promise<GenerateImagesResponse> {
    try {
      const response = await this.client.models.generateImages({
        model,
        prompt,
        config,
      });
      return response;
    } catch (e) {
      console.error('Image generation failed:', e);
      // It's often better to let the caller handle UI-specific error messages,
      // but re-throw the error so it can be caught.
      throw e;
    }
  }
}