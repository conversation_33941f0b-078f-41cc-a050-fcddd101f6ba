/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
import { defineComponent, ref } from 'vue';

export default defineComponent({
  setup() {
    const isExpanded = ref(false);

    const toggleHelp = () => {
      isExpanded.value = !isExpanded.value;
    };

    return {
      isExpanded,
      toggleHelp,
    };
  },
  template: `
    <div class="w-full bg-gray-800 rounded-lg shadow-lg mt-4 text-sm">
      <button @click="toggleHelp" 
              class="w-full flex justify-between items-center p-3 text-left font-semibold text-gray-200 hover:bg-gray-700 rounded-t-lg transition-colors"
              aria-expanded="isExpanded"
              aria-controls="help-content">
        <span>Need Help?</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transform transition-transform duration-200" :class="{'rotate-180': isExpanded}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      <div v-show="isExpanded" id="help-content" class="p-3 border-t border-gray-700 text-gray-300 space-y-2">
        <p><strong>Starting:</strong> Select a genre, then click "Generate Character & Start". An initial scene will be narrated.</p>
        <p><strong>Interacting:</strong> After narration, the microphone icon will activate. Click it to speak to your companion. Click again to stop.</p>
        <p><strong>Companion:</strong> Your AI companion has a unique personality based on the genre. Their image and mood emoji are on the right.</p>
        <p><strong>Interrupting:</strong> You can interrupt your companion while they are speaking by starting to talk (if the mic is active) or by clicking the mic button.</p>
        <p><strong>Contextual Changes:</strong> Try asking your companion to change their mood or speaking style!</p>
        <p><strong>Troubleshooting:</strong> If audio seems stuck, try the "Stop" button then "Start" again. Check your microphone permissions if prompted by the browser.</p>
      </div>
    </div>
  `
});