/**
 * Gemini Live Audio Service for real-time companion voice interaction
 * Based on the working example from Gemini Live API documentation
 */

import { GoogleGenAI, LiveServerMessage, Modality } from '@google/genai';

// Check if we have the API key
const API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
console.log('Gemini Live Audio - API Key check:', API_KEY ? 'Present' : 'Missing');

if (!API_KEY) {
  console.error('VITE_GEMINI_API_KEY is not set. Available env vars:', Object.keys(import.meta.env));
  throw new Error('VITE_GEMINI_API_KEY is not set. Gemini Live Audio requires an API key.');
}

export interface LiveAudioConfig {
  model?: string;
  responseModalities?: Modality[];
}

export interface LiveAudioCallbacks {
  onopen: () => void;
  onmessage: (message: LiveServerMessage) => void;
  onerror: (error: Error) => void;
  onclose: (reason?: string) => void;
  onAudioReceived?: (audioBuffer: AudioBuffer) => void;
  onTextReceived?: (text: string) => void;
}

export class GeminiLiveAudioService {
  private client: GoogleGenAI;
  private session: any = null;
  private audioContext: AudioContext | null = null;
  private callbacks: LiveAudioCallbacks | null = null;
  private responseQueue: LiveServerMessage[] = [];
  private audioChunks: Int16Array[] = [];
  private isPlayingAudio: boolean = false;
  private currentAudioSource: AudioBufferSourceNode | null = null;

  constructor() {
    this.client = new GoogleGenAI({ apiKey: API_KEY });
  }

  /**
   * Connect to Gemini Live Audio session
   */
  async connect(config: LiveAudioConfig, callbacks: LiveAudioCallbacks): Promise<void> {
    try {
      this.callbacks = callbacks;

      console.log('Connecting to Gemini Live Audio...');

      const model = config.model || 'gemini-2.0-flash-live-001';

      console.log('Connecting with model:', model);
      console.log('Config responseModalities:', config.responseModalities);

      this.session = await this.client.live.connect({
        model: model,
        callbacks: {
          onopen: () => {
            console.log('Gemini Live Audio session opened');
            callbacks.onopen();
          },
          onmessage: (message: LiveServerMessage) => {
            console.log('Received message:', {
              hasServerContent: !!message.serverContent,
              turnComplete: message.serverContent?.turnComplete,
              hasModelTurn: !!message.serverContent?.modelTurn,
              hasParts: !!message.serverContent?.modelTurn?.parts,
              partsCount: message.serverContent?.modelTurn?.parts?.length || 0
            });
            this.responseQueue.push(message);
            this.handleMessage(message);
            callbacks.onmessage(message);
          },
          onerror: (error: any) => {
            console.error('Gemini Live Audio error:', error);
            callbacks.onerror(new Error(error.message || 'Live audio error'));
          },
          onclose: (event: any) => {
            console.log('Gemini Live Audio session closed:', event?.reason);
            this.cleanup();
            callbacks.onclose(event?.reason);
          }
        },
        config: {
          responseModalities: config.responseModalities || [Modality.AUDIO]
        }
      });

      console.log('Gemini Live Audio connected successfully');

    } catch (error) {
      console.error('Failed to connect to Gemini Live Audio:', error);
      throw new Error(`Live audio connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Send text message to the session
   */
  sendText(text: string): void {
    if (!this.session) {
      console.warn('No active session to send text');
      return;
    }

    try {
      console.log('Attempting to send text to Gemini Live:', text);
      this.session.sendClientContent({ turns: text });
      console.log('Successfully sent text to Gemini Live:', text);
    } catch (error) {
      console.error('Failed to send text:', error);
      throw error;
    }
  }

  /**
   * Handle incoming messages from Gemini Live - focus only on audio processing
   */
  private handleMessage(message: LiveServerMessage): void {
    try {
      // Only handle audio responses internally, let the original callback handle text
      if (message.serverContent?.modelTurn?.parts) {
        for (const part of message.serverContent.modelTurn.parts) {
          // Handle audio response - collect chunks instead of playing immediately
          if (part.inlineData?.mimeType?.startsWith('audio/') && part.inlineData.data) {
            console.log('Collecting audio chunk from Gemini Live');
            // Convert base64 to Int16Array immediately
            const audioChunk = this.base64ToInt16Array(part.inlineData.data);
            this.audioChunks.push(audioChunk);
          }
        }
      }

      // Check for turn completion at the message level (not just modelTurn level)
      if (message.serverContent?.turnComplete) {
        console.log(`Turn complete detected with ${this.audioChunks.length} audio chunks`);
        if (this.audioChunks.length > 0) {
          console.log(`Playing complete audio response with ${this.audioChunks.length} chunks`);
          this.playCollectedAudioChunks();
          this.audioChunks = []; // Clear chunks for next response
        }
      }
    } catch (error) {
      console.error('Error handling message:', error);
    }
  }

  /**
   * Convert base64 audio data to Int16Array
   */
  private base64ToInt16Array(base64Audio: string): Int16Array {
    const binaryString = atob(base64Audio);
    const audioBytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      audioBytes[i] = binaryString.charCodeAt(i);
    }
    return new Int16Array(audioBytes.buffer, audioBytes.byteOffset, audioBytes.byteLength / Int16Array.BYTES_PER_ELEMENT);
  }

  /**
   * Play collected audio chunks as a single continuous response
   */
  private async playCollectedAudioChunks(): Promise<void> {
    try {
      if (this.audioChunks.length === 0) {
        console.warn('No audio chunks to play');
        return;
      }

      // Stop any currently playing audio
      if (this.currentAudioSource) {
        this.currentAudioSource.stop();
        this.currentAudioSource = null;
      }

      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      }

      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // Calculate total length for concatenated audio
      const totalLength = this.audioChunks.reduce((sum, chunk) => sum + chunk.length, 0);

      // Concatenate all audio chunks into a single Int16Array
      const concatenatedAudio = new Int16Array(totalLength);
      let offset = 0;
      for (const chunk of this.audioChunks) {
        concatenatedAudio.set(chunk, offset);
        offset += chunk.length;
      }

      // Create audio buffer (24kHz, mono, 16-bit PCM)
      const audioBuffer = this.audioContext.createBuffer(1, concatenatedAudio.length, 24000);
      const channelData = audioBuffer.getChannelData(0);

      // Convert Int16 to Float32 and copy to audio buffer
      for (let i = 0; i < concatenatedAudio.length; i++) {
        channelData[i] = concatenatedAudio[i] / 32768.0; // Convert to [-1, 1] range
      }

      // Play the audio
      this.isPlayingAudio = true;
      this.currentAudioSource = this.audioContext.createBufferSource();
      this.currentAudioSource.buffer = audioBuffer;
      this.currentAudioSource.connect(this.audioContext.destination);

      // Set up completion handler
      this.currentAudioSource.onended = () => {
        this.isPlayingAudio = false;
        this.currentAudioSource = null;
        console.log('Audio playback completed');
      };

      this.currentAudioSource.start(0);

      // Notify callback if available
      if (this.callbacks?.onAudioReceived) {
        this.callbacks.onAudioReceived(audioBuffer);
      }

      console.log('Playing concatenated audio response from Gemini Live');

    } catch (error) {
      console.error('Failed to play audio response:', error);
      this.isPlayingAudio = false;
      this.currentAudioSource = null;
    }
  }

  /**
   * Disconnect from the session
   */
  async disconnect(): Promise<void> {
    try {
      if (this.session) {
        await this.session.close();
        this.session = null;
      }

      this.cleanup();
      console.log('Disconnected from Gemini Live Audio');

    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  }

  /**
   * Clean up resources
   */
  private cleanup(): void {
    // Stop any currently playing audio
    if (this.currentAudioSource) {
      this.currentAudioSource.stop();
      this.currentAudioSource = null;
    }

    if (this.audioContext) {
      this.audioContext.close().catch(console.error);
      this.audioContext = null;
    }

    this.callbacks = null;
    this.responseQueue = [];
    this.audioChunks = [];
    this.isPlayingAudio = false;
  }

  /**
   * Check if currently connected
   */
  isConnected(): boolean {
    return this.session !== null;
  }

  /**
   * Stop any currently playing audio
   */
  stopAudio(): void {
    if (this.currentAudioSource) {
      this.currentAudioSource.stop();
      this.currentAudioSource = null;
      this.isPlayingAudio = false;
      console.log('Stopped audio playback');
    }
  }

  /**
   * Check if audio is currently playing
   */
  isAudioPlaying(): boolean {
    return this.isPlayingAudio;
  }
}
