<template>
  <div class="adventure-screen">
    <!-- Background Image -->
    <div
      class="background-image"
      :style="{
        background: currentSceneImage.startsWith('linear-gradient')
          ? currentSceneImage
          : `url(${currentSceneImage})`
      }"
    ></div>
    
    <div class="content-container">
      <!-- Main Game Area -->
      <div class="game-area">
        <!-- Message Log -->
        <div class="message-log" ref="messageLog">
          <div 
            v-for="message in state.messages" 
            :key="message.id"
            class="message"
            :class="[
              `message--${message.type}`,
              { 'message--with-image': message.imageUrl }
            ]"
          >
            <div class="message-content">
              <div v-if="message.type === 'narrator'" class="message-narrator">
                <span class="message-label">Narrator</span>
                <p>{{ message.content }}</p>
              </div>
              
              <div v-else-if="message.type === 'companion'" class="message-companion">
                <div class="companion-header">
                  <img 
                    :src="companionImage" 
                    class="companion-avatar"
                    :alt="state.companion?.name"
                  >
                  <div>
                    <span class="message-label">{{ state.companion?.name }}</span>
                    <span class="mood-indicator" :title="getMoodDescription((message.mood as Mood) || 'neutral')">
                      {{ getMoodEmoji((message.mood as Mood) || 'neutral') }}
                    </span>
                  </div>
                </div>
                <p>{{ message.content }}</p>
              </div>
              
              <div v-else-if="message.type === 'player'" class="message-player">
                <span class="message-label">You</span>
                <p>{{ message.content }}</p>
              </div>
              
              <img 
                v-if="message.imageUrl" 
                :src="message.imageUrl" 
                class="message-image" 
                alt="Scene"
              >
            </div>
          </div>
        </div>
        
        <!-- Input Area -->
        <div class="input-area">
          <div class="relative flex-1">
            <input
              v-model="userInput"
              type="text"
              class="w-full p-4 pr-16 rounded-lg bg-white bg-opacity-10 border border-white border-opacity-20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="What would you like to do or say?"
              @keyup.enter="handleUserInput"
              :disabled="state.isNarrating || state.isCompanionSpeaking"
            />
            <button
              class="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-white hover:text-purple-300 focus:outline-none"
              :class="{ 'opacity-50 cursor-not-allowed': !userInput.trim() }"
              :disabled="!userInput.trim() || state.isNarrating || state.isCompanionSpeaking"
              @click="handleUserInput"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </button>
          </div>
          
          <div class="flex items-center justify-between mt-4 text-sm text-gray-400">
            <div class="flex items-center space-x-2">
              <span class="flex items-center">
                <span class="w-3 h-3 rounded-full bg-green-500 mr-1"></span>
                {{ state.isPlayerSpeaking ? 'Listening...' : 'Tap to speak' }}
              </span>
              <button 
                class="p-1 hover:text-white"
                @click="toggleVoiceInput"
                :disabled="state.isNarrating || state.isCompanionSpeaking"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </button>
            </div>
            <button 
              class="text-xs bg-black bg-opacity-30 px-2 py-1 rounded hover:bg-opacity-50"
              @click="showHelp = !showHelp"
            >
              {{ showHelp ? 'Hide Help' : 'Show Help' }}
            </button>
          </div>
          
          <!-- Help Panel -->
          <transition
            enter-active-class="transition-all duration-200 ease-out"
            leave-active-class="transition-all duration-150 ease-in"
            enter-from-class="opacity-0 transform -translate-y-2"
            leave-to-class="opacity-0 transform -translate-y-2"
          >
            <div v-if="showHelp" class="mt-4 p-4 bg-black bg-opacity-50 rounded-lg text-sm">
              <h3 class="font-bold text-purple-300 mb-2">How to Play</h3>
              <ul class="space-y-2">
                <li>• <span class="font-medium">Actions:</span> Describe what you want to do (e.g., "I open the door", "Let's go left")</li>
                <li>• <span class="font-medium">Conversation:</span> Talk to your companion (e.g., "What do you think?", "Tell me about yourself")</li>
                <li>• <span class="font-medium">Voice Input:</span> Click the microphone to speak instead of type</li>
                <li>• <span class="font-medium">Relationship:</span> Your choices affect how your companion feels about you</li>
              </ul>
            </div>
          </transition>
        </div>
      </div>
      
      <!-- Speech Error Display -->
      <div v-if="speechError" class="speech-error">
        <div class="error-message">
          {{ speechError }}
        </div>
      </div>
      
      <!-- Companion Panel -->
      <div class="companion-panel">
        <div class="companion-card">
          <div class="companion-image-container">
            <img 
              :src="companionImage" 
              class="companion-image" 
              :alt="state.companion?.name"
            />
            <div class="companion-mood">
              {{ getMoodEmoji((state.companion?.mood as Mood) || 'neutral') }}
            </div>
          </div>
          
          <div class="companion-details">
            <h3 class="companion-name">{{ state.companion?.name }}</h3>
            <p class="companion-description">{{ state.companion?.description }}</p>
            
            <div class="relationship-meter">
              <div class="relationship-label">Relationship</div>
              <div class="relationship-bar">
                <div 
                  class="relationship-fill"
                  :style="{ width: relationshipPercentage + '%' }"
                  :class="relationshipStatus.class"
                ></div>
              </div>
              <div class="relationship-status">
                {{ relationshipStatus.text }}
              </div>
            </div>
            
            <div class="companion-traits">
              <div class="trait" v-for="(value, index) in state.companion?.values" :key="index">
                <span class="trait-badge">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="voice-wave" :class="{ 'voice-wave--active': state.isNarrating || state.isCompanionSpeaking || state.isPlayerSpeaking }">
          <div class="voice-wave-bar" v-for="i in 5" :key="i"></div>
        </div>
      </div>
    </div>
    
    <!-- LiveAudio Component for TTS and Live Audio -->
    <LiveAudio
      ref="liveAudio"
      :initial-message="state.messages[0]?.content || 'Hello, how can I help you today?'"
      :text-model="state.settings.liveModel"
      :tts-model="state.settings.ttsModel"
      :voice="state.settings.voice"
      :interrupt-sensitivity="state.settings.interruptSensitivity"
      @speaking-start="handleSpeakingStart"
      @speaking-end="handleSpeakingEnd"
      @error="handleAudioError"
      @companion-message="handleCompanionMessage"
      style="display: none;"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useGameState } from '../core/state/gameState';
import LiveAudio from './LiveAudio.vue';
import { Mood } from '../types/index';

declare global {
  interface Window {
    webkitSpeechRecognition: any;
    SpeechRecognition: any;
  }
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  onresult: ((event: any) => void) | null;
  onerror: ((event: any) => void) | null;
  onend: (() => void) | null;
  onspeechend: (() => void) | null;
  start(): void;
  stop(): void;
}

export default defineComponent({
  name: 'AdventureScreen',
  components: {
    LiveAudio
  },
  
  setup() {
    const {
      state,
      addMessage,
      updateCompanionMood,
      getMoodEmoji,
      getMoodDescription,
      startCompanionSpeech,
      stopCompanionSpeech,
      // startPlayerSpeech,
      // stopPlayerSpeech
    } = useGameState();
    const userInput = ref('');
    const showHelp = ref(true);
    const messageLog = ref<HTMLElement | null>(null);
    const liveAudio = ref<any>(null);
    const isSpeaking = ref(false);
    const speakResolve = ref<(() => void) | null>(null);
    
    // Scroll to bottom of message log when new messages arrive
    watch(() => state.messages.length, () => {
      nextTick(() => {
        if (messageLog.value) {
          messageLog.value.scrollTop = messageLog.value.scrollHeight;
        }
      });
    }, { immediate: true });
    
    // Create local handlers for audio events
    const handleSpeakingStart = () => {
      isSpeaking.value = true;
      isProcessingTts.value = true; // Disable speech recognition during any AI speech
      console.log('AI started speaking, disabling speech recognition');
      startCompanionSpeech();
    };

    const handleSpeakingEnd = () => {
      isSpeaking.value = false;
      isProcessingTts.value = false; // Re-enable speech recognition after AI speech
      console.log('AI finished speaking, re-enabling speech recognition');
      if (speakResolve.value) {
        speakResolve.value();
        speakResolve.value = null;
      }
      stopCompanionSpeech();
    };

    const handleAudioError = (error: Error) => {
      console.error('Audio error:', error);
      isSpeaking.value = false;
      if (speakResolve.value) {
        speakResolve.value();
        speakResolve.value = null;
      }
      // Just log the error, no specific handler needed
    };

    const handleCompanionMessage = (message: string) => {
      console.log('Received companion message from Live Audio:', message);

      // Determine mood based on message content (simple heuristic)
      let mood: Mood = 'neutral';
      const messageLower = message.toLowerCase();
      if (messageLower.includes('hello') || messageLower.includes('greetings') || messageLower.includes('welcome')) {
        mood = 'happy';
      } else if (messageLower.includes('help') || messageLower.includes('assist')) {
        mood = 'happy'; // Use 'happy' instead of 'helpful' since it's not in the Mood type
      } else if (messageLower.includes('think') || messageLower.includes('consider')) {
        mood = 'thinking';
      } else if (messageLower.includes('exciting') || messageLower.includes('adventure')) {
        mood = 'excited';
      }

      // Add companion message to the chat
      addMessage({
        type: 'companion' as const,
        content: message,
        mood: mood
      });

      // Update companion's mood
      if (state.companion) {
        updateCompanionMood(mood);
      }
    };

    // This function is no longer needed as we use liveAudio.value.speakText directly
    // const speakText = async (text: string) => {
    //   if (!liveAudio.value) return;
    //
    //   return new Promise<void>((resolve) => {
    //     speakResolve.value = resolve;
    //     liveAudio.value.speakText(text);
    //   });
    // };

    // Computed properties
    const currentSceneImage = computed(() => {
      // Use a placeholder gradient instead of external images
      const theme = state.currentTheme?.id || 'fantasy';
      const gradients = {
        fantasy: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        scifi: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        mystery: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        horror: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
        western: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
      };
      return state.currentScene?.imageUrl || gradients[theme as keyof typeof gradients] || gradients.fantasy;
    });

    const companionImage = computed(() => {
      // Use a placeholder avatar instead of external images
      const theme = state.currentTheme?.id || 'fantasy';
      const avatars = {
        fantasy: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjNjY3ZWVhIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+8J+nmTwvdGV4dD4KPC9zdmc+',
        scifi: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZjA5M2ZiIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+8J+klzwvdGV4dD4KPC9zdmc+',
        mystery: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjNGZhY2ZlIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+8J+VtTwvdGV4dD4KPC9zdmc+',
        horror: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjNDNlOTdiIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+8J+UpTwvdGV4dD4KPC9zdmc+',
        western: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZmE3MDlhIi8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+8J+klDwvdGV4dD4KPC9zdmc+'
      };
      return avatars[theme as keyof typeof avatars] || avatars.fantasy;
    });
    
    const relationshipPercentage = computed(() => {
      // Convert -100 to 100 range to 0-100%
      return ((state.companion?.relationshipScore || 0) + 100) / 2;
    });
    
    const relationshipStatus = computed(() => {
      const score = state.companion?.relationshipScore || 0;
      
      if (score < -50) return { text: 'Hostile', class: 'bg-red-500' };
      if (score < 0) return { text: 'Distrustful', class: 'bg-orange-500' };
      if (score === 0) return { text: 'Neutral', class: 'bg-yellow-500' };
      if (score < 50) return { text: 'Friendly', class: 'bg-blue-400' };
      return { text: 'Loyal', class: 'bg-green-500' };
    });
    
    // Mood-related methods are imported from useGameState()
    
    const handleUserInput = () => {
      const input = userInput.value.trim();
      if (!input || state.isNarrating || state.isCompanionSpeaking) return;
      
      // Add user message to log
      addMessage({
        type: 'player',
        content: input
      });
      
      // Process the input
      processInput(input);
      
      // Clear input
      userInput.value = '';
    };
    
    const processInput = async (input: string) => {
      // Show typing indicator using addMessage
      const companionTyping = {
        type: 'companion' as const,
        content: '...',
        mood: 'thinking' as Mood,
        isTyping: true
      };

      // Add typing indicator using the composable method
      addMessage(companionTyping);

      try {
        // Send input to Live Audio companion instead of generating local response
        if (liveAudio.value && liveAudio.value.isConnected()) {
          console.log('Sending input to Live Audio companion:', input);
          liveAudio.value.sendTextToCompanion(input);

          // Remove typing indicator - the companion will respond via Live Audio
          addMessage({
            ...companionTyping,
            isTyping: false
          });
        } else {
          console.log('Live Audio not connected, using fallback local response');

          // Fallback to local response generation if Live Audio is not available
          await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

          // Generate response based on input
          const inputLower = input.toLowerCase();
          let response: string;
          let mood: Mood = 'neutral';

          // Simple response logic - this would be replaced with AI in production
          if (inputLower.includes('hello') || inputLower.includes('hi') || inputLower.includes('hey')) {
            response = `Greetings, traveler! I'm ${state.companion?.name || 'your companion'}. How may I assist you on this journey?`;
            mood = 'happy';
          } else if (inputLower.includes('how are you')) {
            response = `I'm doing well, thank you for asking! The ${state.currentTheme?.name.toLowerCase() || 'adventure'} is full of possibilities today.`;
            mood = 'happy';
          } else if (inputLower.includes('what is your name')) {
            response = `I am ${state.companion?.name || 'your companion'}. I'm here to guide you through this ${state.currentTheme?.name.toLowerCase() || 'adventure'}!`;
            mood = 'excited';
          } else if (inputLower.includes('where are we')) {
            response = `We're at the start of our ${state.currentTheme?.name.toLowerCase() || 'adventure'}. The path ahead is unclear, but together we can discover what lies ahead.`;
            mood = 'thinking';
          } else if (inputLower.includes('thank')) {
            response = `You're most welcome! It's my pleasure to assist you on this journey.`;
            mood = 'happy';
          } else if (inputLower.includes('help')) {
            response = `I can help you navigate this adventure. Try asking about our surroundings, or tell me what you'd like to do next.`;
            mood = 'happy';
          } else {
            // Default response for unrecognized input
            const responses = [
              `I see. Tell me more about that.`,
              `That's interesting. What would you like to do next?`,
              `I understand. How does that make you feel?`,
              `Let me think about that for a moment...`,
              `In this ${state.currentTheme?.name.toLowerCase() || 'realm'}, that could mean many things. Could you elaborate?`
            ];
            response = responses[Math.floor(Math.random() * responses.length)];
            mood = 'thinking';
          }

          // Remove typing indicator
          addMessage({
            ...companionTyping,
            isTyping: false
          });

          // Add companion's response
          addMessage({
            type: 'companion' as const,
            content: response,
            mood: mood
          });

          // Update companion's mood using the composable method if available
          if (state.companion) {
            updateCompanionMood(mood);
          }

          // Use TTS for fallback response
          isProcessingTts.value = true;
          console.log('Starting TTS for fallback response');

          try {
            if (liveAudio.value) {
              await liveAudio.value.speakText(response);
              console.log('TTS completed');
            } else {
              console.warn('LiveAudio component not available');
            }
          } catch (error) {
            console.error('Error with TTS:', error);
          } finally {
            isProcessingTts.value = false;
            console.log('TTS finished, recognition should continue automatically');
          }
        }
      } catch (error) {
        console.error('Error processing input:', error);
        // Ensure typing indicator is removed even if there's an error
        addMessage({
          ...companionTyping,
          isTyping: false
        });
      }
    };
    
    const toggleVoiceInput = () => {
      // This will be implemented with Web Speech API
      alert('Voice input will be implemented soon!');
    };
    
    // Speech recognition
    const recognition = ref<SpeechRecognition | null>(null);
    const isListening = ref(false);
    const isSpeechAvailable = ref(false);
    const speechError = ref<string | null>(null);
    const isProcessingTts = ref(false); // Flag to prevent TTS feedback loop

    // Check for speech recognition support
    onMounted(() => {
      isSpeechAvailable.value = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
      if (!isSpeechAvailable.value) {
        console.warn('Speech recognition not supported in this browser');
        speechError.value = 'Speech recognition is not supported in your browser. Please try Chrome or Edge.';
        return;
      }
    });

    const startListening = () => {
      if (!isSpeechAvailable.value) {
        console.warn('Speech recognition not available');
        speechError.value = 'Speech recognition is not available. Please check your browser permissions.';
        return;
      }

      try {
        // Create new instance each time to prevent issues with Chrome's implementation
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition.value = new SpeechRecognition();

        if (!recognition.value) {
          throw new Error('Failed to create speech recognition instance');
        }

        recognition.value.continuous = true;
        recognition.value.interimResults = true;
        recognition.value.lang = 'en-US';
        speechError.value = null;

        recognition.value.onresult = (event: any) => {
          // Skip processing if we're currently speaking TTS
          if (isProcessingTts.value) {
            console.log('Skipping recognition during TTS');
            return;
          }
          
          let finalTranscript = '';
          
          // Process all results
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript.trim();
            if (event.results[i].isFinal) {
              finalTranscript = transcript;
            }
          }

          // Only process if we have a final transcript and it's not empty
          if (finalTranscript && !isProcessingTts.value) {
            console.log('Processing recognized speech:', finalTranscript);
            userInput.value = finalTranscript;
            // Small delay to ensure UI updates
            setTimeout(() => {
              handleUserInput();
            }, 300);
          }
        };

        recognition.value.onerror = (event: any) => {
          console.error('Speech recognition error:', event.error);

          // Don't stop listening for certain recoverable errors
          if (event.error === 'no-speech' || event.error === 'aborted') {
            console.log('Recoverable error, continuing...');
            return;
          }

          isListening.value = false;

          switch(event.error) {
            case 'no-speech':
              // Don't show error for no-speech, it's normal
              break;
            case 'audio-capture':
              speechError.value = 'No microphone found. Please ensure a microphone is connected.';
              break;
            case 'not-allowed':
              speechError.value = 'Microphone access was denied. Please allow microphone access in your browser settings.';
              break;
            case 'aborted':
              // Don't show error for aborted, it's usually intentional
              break;
            default:
              speechError.value = `Error: ${event.error}`;
          }

          // Auto-clear error after 5 seconds
          if (speechError.value) {
            setTimeout(() => {
              speechError.value = null;
            }, 5000);
          }
        };

        recognition.value.onspeechend = () => {
          // Don't stop listening, just log
          console.log('Speech ended, but keeping recognition active');
        };

        recognition.value.onend = () => {
          if (isListening.value && !isProcessingTts.value) {
            console.log('Recognition ended, restarting...');
            // Small delay to prevent rapid restart cycles
            setTimeout(() => {
              if (isListening.value && recognition.value) {
                try {
                  recognition.value.start();
                } catch (e) {
                  console.error('Error restarting recognition:', e);
                  // If we can't restart, stop and show error
                  isListening.value = false;
                  speechError.value = 'Error accessing microphone. Please refresh the page and try again.';
                }
              }
            }, 100);
          } else if (isProcessingTts.value) {
            console.log('Recognition ended during TTS, will restart after TTS completes');
          }
        };

        // Start recognition
        recognition.value.start();
        isListening.value = true;
        console.log('Speech recognition started');
        
      } catch (error) {
        console.error('Error initializing speech recognition:', error);
        isListening.value = false;
        speechError.value = 'Failed to start speech recognition. Please try again.';
      }
    };

    const stopListening = () => {
      if (recognition.value) {
        try {
          recognition.value.onend = null; // Prevent restart
          recognition.value.stop();
          isListening.value = false;
          console.log('Speech recognition stopped');
        } catch (e) {
          console.error('Error stopping recognition:', e);
        }
      }
    };

    // Lifecycle hooks
    onMounted(() => {
      // Check if speech recognition is available
      isSpeechAvailable.value = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
      
      if (isSpeechAvailable.value) {
        startListening();
      } else {
        console.warn('Speech recognition not supported in this browser');
      }
    });
    
    onUnmounted(() => {
      stopListening();
    });
    
    return {
      state,
      userInput,
      showHelp,
      messageLog,
      liveAudio, // Add this to expose the liveAudio ref
      currentSceneImage,
      companionImage,
      relationshipPercentage,
      relationshipStatus,
      isListening,
      isSpeechAvailable,
      speechError,
      getMoodEmoji,
      getMoodDescription,
      handleUserInput,
      toggleVoiceInput,
      startListening,
      stopListening,
      handleSpeakingStart,
      handleSpeakingEnd,
      handleAudioError,
      handleCompanionMessage
    };
  }
});
</script>

<style scoped>
.adventure-screen {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  color: white;
  display: flex;
  flex-direction: column;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  filter: brightness(0.4);
  z-index: -1;
  transition: background-image 0.5s ease-in-out;
}

.content-container {
  display: flex;
  flex: 1;
  padding: 2rem;
  gap: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.game-area {
  flex: 2;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.message-log {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.message {
  max-width: 80%;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-content {
  padding: 1rem 1.25rem;
  border-radius: 1rem;
  line-height: 1.5;
  position: relative;
  word-wrap: break-word;
}

.message--narrator .message-content {
  background: rgba(255, 255, 255, 0.1);
  border-left: 3px solid #9f7aea;
  margin-left: 0;
  margin-right: auto;
}

.message--companion .message-content {
  background: rgba(99, 102, 241, 0.2);
  border-left: 3px solid #6366f1;
  margin-left: 0;
  margin-right: auto;
  max-width: 90%;
}

.message--player .message-content {
  background: rgba(16, 185, 129, 0.2);
  border-right: 3px solid #10b981;
  margin-left: auto;
  margin-right: 0;
}

.message-label {
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
  display: block;
  opacity: 0.8;
}

.companion-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.companion-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 0.75rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.mood-indicator {
  margin-left: 0.5rem;
  font-size: 1.1em;
  vertical-align: middle;
}

.message-image {
  width: 100%;
  max-height: 300px;
  border-radius: 0.5rem;
  margin-top: 1rem;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.input-area {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.3);
}

.companion-panel {
  flex: 1;
  max-width: 320px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.companion-card {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 1rem;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.companion-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 0.75rem;
  overflow: hidden;
  margin-bottom: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.companion-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.companion-image-container:hover .companion-image {
  transform: scale(1.05);
}

.companion-mood {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(0, 0, 0, 0.7);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  backdrop-filter: blur(4px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.companion-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
}

.companion-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.25rem;
  line-height: 1.5;
}

.relationship-meter {
  margin: 1.5rem 0;
}

.relationship-label {
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
}

.relationship-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.relationship-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease, background-color 0.3s ease;
}

.relationship-status {
  font-size: 0.8rem;
  text-align: right;
  color: rgba(255, 255, 255, 0.8);
}

.companion-traits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.trait-badge {
  background: rgba(159, 122, 234, 0.2);
  color: #c4b5fd;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  border: 1px solid rgba(159, 122, 234, 0.3);
}

.voice-wave {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  height: 60px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0.75rem;
  padding: 0 1.5rem;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.voice-wave--active {
  opacity: 1;
}

.speech-error {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-width: 80%;
  text-align: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translate(-50%, 10px); }
  to { opacity: 1; transform: translate(-50%, 0); }
}

.voice-wave-bar {
  width: 4px;
  height: 20px;
  background-color: #8b5cf6;
  margin: 0 2px;
  border-radius: 2px;
  animation: voiceWave 1.5s ease-in-out infinite;
  opacity: 0.5;
  transform-origin: bottom;
}

.voice-wave-bar:nth-child(1) { animation-delay: 0s; }
.voice-wave-bar:nth-child(2) { animation-delay: 0.2s; }
.voice-wave-bar:nth-child(3) { animation-delay: 0.4s; }
.voice-wave-bar:nth-child(4) { animation-delay: 0.6s; }
.voice-wave-bar:nth-child(5) { animation-delay: 0.8s; }

@keyframes voiceWave {
  0%, 100% { 
    transform: scaleY(0.5); 
    opacity: 0.3; 
  }
  50% { 
    transform: scaleY(2); 
    opacity: 1; 
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .content-container {
    flex-direction: column;
    padding: 1rem;
  }
  
  .companion-panel {
    max-width: 100%;
    margin-top: 1rem;
  }
  
  .message {
    max-width: 90%;
  }
}

@media (max-width: 640px) {
  .message {
    max-width: 100%;
  }
  
  .companion-traits {
    justify-content: center;
  }
}
</style>
