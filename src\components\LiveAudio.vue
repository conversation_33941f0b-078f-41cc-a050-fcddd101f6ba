<template>
  <!-- Hidden component for audio processing -->
  <div style="display: none;"></div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import { StartSensitivity, Modality } from '@google/genai';
import {
  DEFAULT_VOICE,
  DEFAULT_TEXT_MODEL,
  DEFAULT_LIVE_MODEL,
  DEFAULT_TTS_MODEL,
  DEFAULT_INTERRUPT_SENSITIVITY
} from '../config/ai-config';
import { speakText as geminiTtsSpeak } from '../services/geminiTts';
import { GeminiLiveAudioService, type LiveAudioConfig, type LiveAudioCallbacks } from '../services/geminiLiveAudio';

export default defineComponent({
  name: 'LiveAudio',

  props: {
    initialMessage: {
      type: String,
      default: "Hello, how can I help you today?"
    },
    textModel: {
      type: String,
      default: DEFAULT_TEXT_MODEL
    },
    ttsModel: {
      type: String,
      default: DEFAULT_TTS_MODEL
    },
    voice: {
      type: String,
      default: DEFAULT_VOICE
    },
    interruptSensitivity: {
      type: String as () => StartSensitivity,
      default: DEFAULT_INTERRUPT_SENSITIVITY
    }
  },

  emits: ['speaking-start', 'speaking-end', 'error', 'companion-message'],

  setup(props, { emit, expose }) {
    const isRecording = ref(false);
    const status = ref('Ready');
    const error = ref('');
    const isSpeaking = ref(false);
    const isConnected = ref(false);

    // Services - create lazily to avoid API key issues during component setup
    let liveAudioService: GeminiLiveAudioService | null = null;

    const getLiveAudioService = () => {
      if (!liveAudioService) {
        console.log('Creating new GeminiLiveAudioService...');
        try {
          liveAudioService = new GeminiLiveAudioService();
          console.log('GeminiLiveAudioService created successfully');
        } catch (error) {
          console.error('Failed to create GeminiLiveAudioService:', error);
          throw error;
        }
      }
      return liveAudioService;
    };

    // Live Audio callbacks
    const liveAudioCallbacks: LiveAudioCallbacks = {
      onopen: () => {
        console.log('Live audio session opened');
        isConnected.value = true;
        status.value = 'Connected';
      },
      onmessage: async (message) => {
        console.log('Received message from live audio:', message);
        // Handle companion responses
        if (message.serverContent?.modelTurn?.parts) {
          for (const part of message.serverContent.modelTurn.parts) {
            if (part.text) {
              emit('companion-message', part.text);
            }
          }
        }
      },
      onerror: (err) => {
        console.error('Live audio error:', err);
        error.value = err.message;
        emit('error', err);
        status.value = 'Error';
      },
      onclose: () => {
        console.log('Live audio session closed');
        isConnected.value = false;
        isRecording.value = false;
        status.value = 'Disconnected';
      },
      onAudioReceived: (audioBuffer) => {
        console.log('Audio received from companion');
        emit('speaking-start');
        isSpeaking.value = true;

        // The audio will play automatically, we just need to track when it ends
        // For now, estimate duration and emit speaking-end
        const duration = audioBuffer.duration * 1000;
        setTimeout(() => {
          isSpeaking.value = false;
          emit('speaking-end');
        }, duration);
      },
      onTextReceived: (text) => {
        console.log('Text received from companion:', text);
        emit('companion-message', text);
      }
    };

    /**
     * Start recording with live audio companion
     */
    const startRecording = async (
      initialMessage: string,
      voice: string,
      textModel: string,
      ttsModel: string,
      interruptSensitivity: StartSensitivity
    ) => {
      try {
        console.log('Starting live audio recording with:', {
          initialMessage, voice, textModel, ttsModel, interruptSensitivity
        });

        error.value = '';
        status.value = 'Connecting...';

        // Configure live audio - use the Live model for Live Audio
        const config: LiveAudioConfig = {
          model: DEFAULT_LIVE_MODEL, // Use the Live model for Live Audio
          responseModalities: [Modality.AUDIO], // Start with audio only to avoid invalid argument error
          interruptSensitivity: interruptSensitivity // Pass interrupt sensitivity for automatic activity detection
        };

        console.log('Live Audio config:', config);
        console.log('DEFAULT_LIVE_MODEL:', DEFAULT_LIVE_MODEL);
        console.log('textModel param:', textModel);

        // Connect to live audio service
        console.log('Getting Live Audio service...');
        const service = getLiveAudioService();
        console.log('Live Audio service obtained, connecting...');

        await service.connect(config, liveAudioCallbacks);
        console.log('Live Audio service connected');

        // Don't send initial message immediately - wait for user input instead
        // if (initialMessage?.trim()) {
        //   console.log('Sending initial message:', initialMessage);
        //   service.sendText(initialMessage);
        // }

        isRecording.value = true;
        status.value = 'Recording...';
        console.log('Live Audio recording started successfully');

      } catch (err) {
        console.error('Failed to start recording:', err);
        error.value = err instanceof Error ? err.message : 'Failed to start recording';
        emit('error', err instanceof Error ? err : new Error('Failed to start recording'));
        status.value = 'Error';
      }
    };

    /**
     * Stop recording (placeholder for now)
     */
    const stopRecording = async () => {
      console.log('Stop recording called (not implemented yet)');
      isRecording.value = false;
      status.value = isConnected.value ? 'Connected' : 'Stopped';
    };

    /**
     * Speak text using Gemini TTS (for narration)
     */
    const speakText = async (text: string) => {
      try {
        console.log('Speaking text via TTS:', text.substring(0, 50) + '...');
        emit('speaking-start');
        isSpeaking.value = true;

        // Use Gemini TTS for narration
        await geminiTtsSpeak(text, {
          model: props.ttsModel,
          voiceName: 'Kore' // Use a consistent voice for narration
        });

        console.log('TTS completed');

      } catch (err) {
        console.error('TTS error:', err);
        error.value = err instanceof Error ? err.message : 'TTS failed';
        emit('error', err instanceof Error ? err : new Error('TTS failed'));
      } finally {
        isSpeaking.value = false;
        emit('speaking-end');
      }
    };

    /**
     * Stop speaking
     */
    const stopSpeaking = () => {
      console.log('Stopping speech');
      isSpeaking.value = false;
      emit('speaking-end');
      // Note: We can't easily stop ongoing TTS, but we can track state
    };

    /**
     * Send text to companion (via live audio)
     */
    const sendTextToCompanion = (text: string) => {
      const service = getLiveAudioService();
      if (service.isConnected()) {
        service.sendText(text);
      } else {
        console.warn('Live audio not connected, cannot send text to companion');
      }
    };

    /**
     * Disconnect from live audio
     */
    const disconnect = async () => {
      try {
        if (liveAudioService) {
          await liveAudioService.disconnect();
        }
        isConnected.value = false;
        isRecording.value = false;
        status.value = 'Disconnected';
      } catch (err) {
        console.error('Error disconnecting:', err);
      }
    };

    // Auto-start Live Audio session when component mounts
    onMounted(async () => {
      try {
        console.log('Auto-starting Live Audio session...');
        console.log('Props:', {
          initialMessage: props.initialMessage,
          voice: props.voice,
          textModel: props.textModel,
          ttsModel: props.ttsModel,
          interruptSensitivity: props.interruptSensitivity
        });

        await startRecording(
          props.initialMessage,
          props.voice,
          props.textModel,
          props.ttsModel,
          props.interruptSensitivity
        );

        console.log('Live Audio session started successfully');
      } catch (error) {
        console.error('Failed to auto-start Live Audio:', error);
        error.value = error instanceof Error ? error.message : 'Failed to start Live Audio';
      }
    });

    // Cleanup on unmount
    onUnmounted(() => {
      disconnect();
    });

    // Expose methods to parent component
    expose({
      startRecording,
      stopRecording,
      speakText,
      stopSpeaking,
      sendTextToCompanion,
      disconnect,
      isConnected: () => isConnected.value,
      isRecording: () => isRecording.value,
      isSpeaking: () => isSpeaking.value
    });

    return {
      isRecording,
      status,
      error,
      isSpeaking,
      isConnected
    };
  }
});
</script>

<style scoped>
/* Hidden component styles */
</style>